{"name": "orbitum", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "yarn kill-port && nest start", "start:dev": "yarn kill-port && nest start --watch", "start:debug": "yarn kill-port && nest start --debug --watch", "start:prod": "node dist/main", "dev": "nodemon", "dev:debug": "nodemon --config nodemon-debug.json", "kill-port": "lsof -ti:3001 | xargs kill -9 2>/dev/null || echo 'No process found on port 3001'", "kill-all-node": "pkill -f 'ts-node.*src/main.ts' 2>/dev/null || echo 'No ts-node processes found'", "kill-nest": "pkill -f 'nest start' 2>/dev/null || echo 'No nest processes found'", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "prisma:generate": "npx prisma generate", "prisma:migrate": "npx prisma migrate dev", "prisma:studio": "npx prisma studio", "deploy": "flyctl deploy", "deploy:staging": "flyctl deploy --config fly.staging.toml"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/config": "^4.0.2", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^10.0.0", "@nestjs/swagger": "^11.2.0", "@prisma/client": "^6.15.0", "@sendgrid/mail": "^8.1.5", "bcrypt": "^6.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "prisma": "^6.15.0", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcrypt": "^6.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^24.3.0", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "nodemon": "^3.1.10", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}