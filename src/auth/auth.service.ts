import { Injectable, UnauthorizedException } from '@nestjs/common';
import * as bcrypt from 'bcrypt';
import { UsersService } from '../users/users.service';
import { JwtService } from '@nestjs/jwt';
import { LoginDto } from './dto/login.dto';
import { LoginResponseDto } from './dto/login-response.dto';
import { UserResponseDto } from '../users/dto/user-response.dto';

@Injectable()
export class AuthService {
  private readonly JWT_EXPIRY = '24h';
  private readonly TOKEN_EXPIRATION_SECONDS = 24 * 60 * 60;

  constructor(
    private readonly usersService: UsersService,
    private readonly jwtService: JwtService,
  ) {}

  async validateUser(emailOrUsername: string, password: string): Promise<any> {
    try {
      const user =
        await this.usersService.findUserByEmailOrUsername(emailOrUsername);

      if (!user) {
        return null;
      }

      if (user.status !== 'ACTIVE') {
        return null;
      }
      const isPasswordValid = await bcrypt.compare(password, user.password);

      return isPasswordValid ? user : null;
    } catch (error) {
      console.error('Error in validateUser:', error);
      return null;
    }
  }

  async login(loginDto: LoginDto): Promise<LoginResponseDto> {
    const { emailOrUsername, password } = loginDto;

    if (!emailOrUsername || !password) {
      throw new UnauthorizedException(
        'Email/username and password are required',
      );
    }

    const user = await this.validateUser(emailOrUsername, password);

    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }
    const payload = this.createJwtPayload(user);
    const accessToken = this.generateAccessToken(payload);

    const userResponse = this.createUserResponse(user);

    return new LoginResponseDto(
      accessToken,
      this.TOKEN_EXPIRATION_SECONDS,
      userResponse,
    );
  }

  private createJwtPayload(user: any): any {
    return {
      sub: user.id,
      email: user.email,
      username: user.username,
      type: user.type,
      status: user.status,
    };
  }

  private generateAccessToken(payload: any): string {
    return this.jwtService.sign(payload, {
      expiresIn: this.JWT_EXPIRY,
    });
  }

  private createUserResponse(user: any): UserResponseDto {
    return new UserResponseDto({
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      username: user.username,
      type: user.type as any,
      businessType: user.businessType as any,
      status: user.status as any,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      password: user.password,
    });
  }

  async validateOAuthLogin(
    email: string,
    provider: string,
    providerId: string,
    accessToken: string,
  ): Promise<any> {
    let user = await this.usersService.findByEmail(email);
    if (!user) {
      // Buat user baru
      const newUser = await this.usersService.create({
        email,
        username: email.split('@')[0],
        password: '', // atau hash acak
        type: 'CONSUMER', // default
        googleId: providerId,
        oauthProvider: provider,
        oauthAccessToken: accessToken,
        status: 'ACTIVE',
      });
      user = newUser;
    } else {
      // Update token jika ada
      if (accessToken) {
        await this.usersService.update(user.id, {
          oauthAccessToken: accessToken,
        });
      }
    }
    return user;
  }
}
