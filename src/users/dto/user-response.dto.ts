import { ApiProperty } from '@nestjs/swagger';
import { Exclude } from 'class-transformer';

export enum UserType {
  CONSUMER = 'CONSUMER',
  BUSINESS = 'BUSINESS',
}

export enum BusinessType {
  RETAIL = 'RETAIL',
  MERCHANT = 'MERCHANT',
}

export enum Status {
  ACTIVE = 'ACTIVE',
  PENDING = 'PENDING',
  FREEZE = 'FREEZE',
  DEACTIVE = 'DEACTIVE',
}

export class UserResponseDto {
  @ApiProperty({ description: 'Unique identifier for the user' })
  id: string;

  @ApiProperty({ description: 'Email address of the user' })
  email: string;

  @ApiProperty({ description: 'Username of the user' })
  username: string;

  @ApiProperty({ enum: UserType, description: 'Type of the user' })
  type: UserType;

  @ApiProperty({
    enum: BusinessType,
    required: false,
    description: 'Business type for business users',
  })
  businessType?: BusinessType;

  @ApiProperty({ enum: Status, description: 'Current status of the user' })
  status: Status;

  @ApiProperty({ description: 'Account creation timestamp' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  updatedAt: Date;

  @Exclude()
  password: string;

  constructor(partial: Partial<UserResponseDto>) {
    Object.assign(this, partial);
  }
}
